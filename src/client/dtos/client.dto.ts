import { Client, ClientStatus } from '@prisma/client';
import {
  IsNotEmpty,
  IsOptional,
  IsEmail,
  Matches,
  IsEnum,
} from 'class-validator';

export class CreateClientDto {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  industry: string;

  @IsOptional()
  location: string;

  @IsOptional()
  summary: string;

  @IsOptional()
  // @Matches(/^\+?[1-9]\d{0,2}[ .-]?\(?\d{3}\)?[ .-]?\d{3}[ .-]?\d{4}$/, {
  //   message: 'Phone number must be in a valid international format',
  // })
  phone: string;

  @IsNotEmpty()
  @IsEnum(ClientStatus)
  status: ClientStatus;
}

export interface ClientFilterType {
  items_per_page?: number;
  page?: number;
  search?: string;
  status?: ClientStatus;
}

export interface ClientPaginationResponseType {
  data: Client[];
  total: number;
  currentPage: number;
  itemsPerPage: number;
}

export class UpdateClientDto {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  email: string;

  @IsOptional()
  industry: string;

  @IsOptional()
  location: string;

  @IsOptional()
  phone: string;

  @IsOptional()
  summary: string;

  @IsOptional()
  @IsEnum(ClientStatus)
  status: ClientStatus;
}
