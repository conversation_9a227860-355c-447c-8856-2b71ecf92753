import {
  Job,
  JobStatus,
  JobType,
  SalaryCurrency,
  SalaryPeriod,
  WorkLocation,
} from '@prisma/client';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsArray,
  IsOptional,
  IsString,
  IsDate,
  ValidateNested,
} from 'class-validator';

import { Type } from 'class-transformer';
import { CreateCandidateDto } from '../../candidate/dtos/candidate.dto';

export class CreateJobDto {
  @IsNotEmpty()
  title: string;

  @IsOptional()
  description: string;

  @IsNotEmpty()
  @IsNumber()
  clientId: number;

  @IsNotEmpty()
  @IsEnum(JobStatus)
  status: JobStatus;

  @IsNotEmpty()
  @IsEnum(JobType)
  type: JobType;

  @IsNotEmpty()
  @IsEnum(WorkLocation)
  workLocation: WorkLocation;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keySkills: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  responsibilities: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requirements: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  benefits: string[];

  @IsOptional()
  @IsNumber()
  salaryMin: number;

  @IsOptional()
  @IsNumber()
  salaryMax: number;

  @IsOptional()
  @IsEnum(SalaryCurrency)
  salaryCurrency: SalaryCurrency;

  @IsOptional()
  @IsEnum(SalaryPeriod)
  salaryPeriod: SalaryPeriod;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate: Date;
}

export interface JobFilterType {
  items_per_page?: number;
  page?: number;
  search?: string;
  status?: JobStatus;
  clientId?: number;
}

export interface JobPaginationResponseType {
  data: Job[];
  total: number;
  currentPage: number;
  itemsPerPage: number;
}

export class UpdateJobDto {
  @IsNotEmpty()
  title: string;

  @IsOptional()
  description: string;

  @IsNotEmpty()
  @IsNumber()
  clientId: number;

  @IsNotEmpty()
  @IsEnum(JobStatus)
  status: JobStatus;

  @IsNotEmpty()
  @IsEnum(JobType)
  type: JobType;

  @IsNotEmpty()
  @IsEnum(WorkLocation)
  workLocation: WorkLocation;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keySkills: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  responsibilities: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requirements: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  benefits: string[];

  @IsOptional()
  @IsNumber()
  salaryMin: number;

  @IsOptional()
  @IsNumber()
  salaryMax: number;

  @IsOptional()
  @IsEnum(SalaryCurrency)
  salaryCurrency: SalaryCurrency;

  @IsOptional()
  @IsEnum(SalaryPeriod)
  salaryPeriod: SalaryPeriod;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate: Date;
}

export class MigrationJobDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsNumber()
  clientId: number;

  @IsOptional()
  @IsEnum(JobStatus)
  status: JobStatus;

  @IsOptional()
  @IsEnum(JobType)
  type: JobType;

  @IsOptional()
  @IsEnum(WorkLocation)
  workLocation: WorkLocation;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keySkills: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  responsibilities: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requirements: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  benefits: string[];

  @IsOptional()
  @IsNumber()
  salaryMin: number;

  @IsOptional()
  @IsNumber()
  salaryMax: number;

  @IsOptional()
  @IsEnum(SalaryCurrency)
  salaryCurrency: SalaryCurrency;

  @IsOptional()
  @IsEnum(SalaryPeriod)
  salaryPeriod: SalaryPeriod;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  startDate: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  endDate: Date;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateCandidateDto)
  candidates: CreateCandidateDto[];
}

export interface MigrationJobResponseType {
  job: Job | null;
  candidatesResult: {
    imported: any[];
    failed: {
      candidate: CreateCandidateDto;
      reason: string;
      status: string;
    }[];
  };
}
