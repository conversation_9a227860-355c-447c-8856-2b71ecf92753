import {
  Req,
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Job, JobStatus } from '@prisma/client';
import { Request } from 'express';
import {
  CreateJobDto,
  JobFilterType,
  JobPaginationResponseType,
  UpdateJobDto,
} from '../job/dtos/job.dto';
import { JobService } from '../job/job.service';
import { BaseResponse } from 'src/common/dto/base-response.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';
import { GetUser } from 'src/auth/decorators/get-user.decorator';
import { UserSession } from 'src/user/dtos/user.dto';

@UseGuards(JwtAuthGuard)
@Controller('jobs')
export class JobController {
  constructor(private jobService: JobService) {}

  @Post()
  create(
    @Req() req: Request,
    @Body() body: CreateJobD<PERSON>,
    @GetUser() user: UserSession,
  ): Promise<BaseResponse<Job | null>> {
    console.log('Create job api =>', body);
    return this.jobService.create(req, body, user);
  }

  @Get()
  getAll(
    @Query() params: JobFilterType,
  ): Promise<BaseResponse<JobPaginationResponseType | null>> {
    console.log('Get all job api', params);
    return this.jobService.getAll(params);
  }

  @Get('basic')
  getAllBasic(@Query('status') status?: JobStatus) {
    console.log('Get all basic job api');
    return this.jobService.getAllBasic(status);
  }

  @Get(':id')
  getDetail(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BaseResponse<Job | null>> {
    console.log('Get job detail api', id);
    return this.jobService.getDetail(id);
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateJobDto,
    @GetUser() user: UserSession,
  ): Promise<BaseResponse<Job | null>> {
    console.log('Update job api', id);
    return this.jobService.update(id, data, user);
  }

  @Delete(':id')
  delete(@Param('id', ParseIntPipe) id: number, @GetUser() user: UserSession) {
    console.log('Delete job', id);
    return this.jobService.delete(id, user);
  }
}
